from flask import Blueprint, request, jsonify
from app.models import get_model
import numpy as np

predict_bp = Blueprint("predict", __name__, url_prefix="/predict")


@predict_bp.route("/", methods=["POST"], strict_slashes=False)
def predict():

    # TODO probably wrong; fix later
    data = request.get_json()

    model = get_model()

    # TODO this will probably need to be refactored as well
    # since this was previously intaking binary data
    # instead of textual input
    try:
        predictions = model.predict(data)
        # Convert NumPy types to Python native types
        predicted_class = int(np.argmax(predictions))
        confidence = float(np.max(predictions))

        return jsonify({
            "class": predicted_class,
            "confidence": confidence
        })
    except ValueError as e:
        return jsonify(error=str(e)), 500
