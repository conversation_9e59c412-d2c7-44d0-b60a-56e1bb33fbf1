[project]
name = "cis325-final"
version = "0.1.0"
package-mode = false
description = ""
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
    {name = "<PERSON>",email = "<EMAIL>"}
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "pandas (>=2.2.3,<3.0.0)",
    "matplotlib (>=3.10.3,<4.0.0)",
    "notebook (>=7.4.2,<8.0.0)",
    "flask (>=3.1.1,<4.0.0)",
    "tensorflow (==2.16.2)",
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
google = "^3.0.0"
google-cloud-bigquery = "^3.33.0"
db-dtypes = "^1.4.3"
google-cloud-bigquery-storage = "^2.31.0"


[tool.poetry.group.macos.dependencies]
tensorflow-macos = "^2.16.2"

