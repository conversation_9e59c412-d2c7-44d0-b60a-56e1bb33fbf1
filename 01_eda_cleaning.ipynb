{"cells": [{"cell_type": "code", "execution_count": 13, "id": "4c38649b-2fa0-4a35-a61f-2afdf37f0ffd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/74/0gvgx3mj70v1rvgs3bb06q5h0000gn/T/ipykernel_36081/**********.py:8: DtypeWarning: Columns (16) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  df = pd.read_csv('data/cms_medicare_outpatient_charges.csv')\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# show real numbers instead of numbers in scientific notation, to the second decimal point\n", "pd.set_option('display.float_format', '{:.2f}'.format)\n", "\n", "df = pd.read_csv('data/cms_medicare_outpatient_charges.csv')"]}, {"cell_type": "code", "execution_count": 14, "id": "604d81ca-1211-4957-b88b-df4e654691ad", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>npi</th>\n", "      <th>nppes_provider_last_org_name</th>\n", "      <th>nppes_provider_first_name</th>\n", "      <th>nppes_provider_city</th>\n", "      <th>nppes_provider_state</th>\n", "      <th>specialty_description</th>\n", "      <th>description_flag</th>\n", "      <th>drug_name</th>\n", "      <th>generic_name</th>\n", "      <th>bene_count</th>\n", "      <th>...</th>\n", "      <th>total_day_supply</th>\n", "      <th>total_drug_cost</th>\n", "      <th>bene_count_ge65</th>\n", "      <th>bene_count_ge65_suppress_flag</th>\n", "      <th>total_claim_count_ge65</th>\n", "      <th>ge65_suppress_flag</th>\n", "      <th>total_day_supply_ge65</th>\n", "      <th>total_drug_cost_ge65</th>\n", "      <th>total_30_day_fill_count</th>\n", "      <th>total_30_day_fill_count_ge65</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>**********</td>\n", "      <td>DENYSIAK</td>\n", "      <td>BARBARA</td>\n", "      <td>SAN DIEGO</td>\n", "      <td>CA</td>\n", "      <td>Family Practice</td>\n", "      <td>S</td>\n", "      <td>METFORMIN HCL</td>\n", "      <td>METFORMIN HCL</td>\n", "      <td>16.00</td>\n", "      <td>...</td>\n", "      <td>3914</td>\n", "      <td>532.20</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>131.00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>**********</td>\n", "      <td>MOORE</td>\n", "      <td>JUSTIN</td>\n", "      <td>ANDERSON</td>\n", "      <td>SC</td>\n", "      <td>Family Practice</td>\n", "      <td>S</td>\n", "      <td>JANUVIA</td>\n", "      <td>SITAGLIPTIN PHOSPHATE</td>\n", "      <td>14.00</td>\n", "      <td>...</td>\n", "      <td>3090</td>\n", "      <td>29815.88</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>103.00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>**********</td>\n", "      <td>NICOLETTI</td>\n", "      <td>ROSANNA</td>\n", "      <td>TUCSON</td>\n", "      <td>AZ</td>\n", "      <td>Family Practice</td>\n", "      <td>S</td>\n", "      <td>OMEGA-3 ACID ETHYL ESTERS</td>\n", "      <td>OMEGA-3 ACID ETHYL ESTERS</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>1021</td>\n", "      <td>5261.92</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>34.90</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>**********</td>\n", "      <td>GIL</td>\n", "      <td>RICHARD</td>\n", "      <td>PORTLAND</td>\n", "      <td>OR</td>\n", "      <td>Internal Medicine</td>\n", "      <td>S</td>\n", "      <td>GLIMEPIRIDE</td>\n", "      <td>GLIMEPIRIDE</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>802</td>\n", "      <td>108.35</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>28.10</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>**********</td>\n", "      <td>HOGAN</td>\n", "      <td>MICHAEL</td>\n", "      <td>SIOUX CITY</td>\n", "      <td>IA</td>\n", "      <td>Cardiology</td>\n", "      <td>S</td>\n", "      <td>SPIRONOLACTONE</td>\n", "      <td>SPIRONOLACTONE</td>\n", "      <td>12.00</td>\n", "      <td>...</td>\n", "      <td>1770</td>\n", "      <td>264.98</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>#</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>59.00</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 21 columns</p>\n", "</div>"], "text/plain": ["          npi nppes_provider_last_org_name nppes_provider_first_name  \\\n", "0  **********                     DENYSIAK                   BARBARA   \n", "1  **********                        MOORE                    JUSTIN   \n", "2  **********                    NICOLETTI                   ROSANNA   \n", "3  **********                          GIL                   RICHARD   \n", "4  **********                        HOGAN                   MICHAEL   \n", "\n", "  nppes_provider_city nppes_provider_state specialty_description  \\\n", "0           SAN DIEGO                   CA       Family Practice   \n", "1            ANDERSON                   SC       Family Practice   \n", "2              TUCSON                   AZ       Family Practice   \n", "3            PORTLAND                   OR     Internal Medicine   \n", "4          SIOUX CITY                   IA            Cardiology   \n", "\n", "  description_flag                  drug_name               generic_name  \\\n", "0                S              METFORMIN HCL              METFORMIN HCL   \n", "1                S                    JANUVIA      SITAGLIPTIN PHOSPHATE   \n", "2                S  OMEGA-3 ACID ETHYL ESTERS  OMEGA-3 ACID ETHYL ESTERS   \n", "3                S                GLIMEPIRIDE                GLIMEPIRIDE   \n", "4                S             SPIRONOLACTONE             SPIRONOLACTONE   \n", "\n", "   bene_count  ...  total_day_supply  total_drug_cost  bene_count_ge65  \\\n", "0       16.00  ...              3914           532.20              NaN   \n", "1       14.00  ...              3090         29815.88              NaN   \n", "2         NaN  ...              1021          5261.92              NaN   \n", "3         NaN  ...               802           108.35              NaN   \n", "4       12.00  ...              1770           264.98              NaN   \n", "\n", "   bene_count_ge65_suppress_flag total_claim_count_ge65  ge65_suppress_flag  \\\n", "0                              #                    NaN                   #   \n", "1                              #                    NaN                   #   \n", "2                              #                    NaN                   #   \n", "3                              #                    NaN                   #   \n", "4                              #                    NaN                   #   \n", "\n", "  total_day_supply_ge65  total_drug_cost_ge65  total_30_day_fill_count  \\\n", "0                   NaN                   NaN                   131.00   \n", "1                   NaN                   NaN                   103.00   \n", "2                   NaN                   NaN                    34.90   \n", "3                   NaN                   NaN                    28.10   \n", "4                   NaN                   NaN                    59.00   \n", "\n", "   total_30_day_fill_count_ge65  \n", "0                           NaN  \n", "1                           NaN  \n", "2                           NaN  \n", "3                           NaN  \n", "4                           NaN  \n", "\n", "[5 rows x 21 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 5, "id": "ff8a342a-7224-4f45-ab87-06421b23825a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape: (24120618, 21)\n", "\n", "Data Types:\n", " npi                                int64\n", "nppes_provider_last_org_name      object\n", "nppes_provider_first_name         object\n", "nppes_provider_city               object\n", "nppes_provider_state              object\n", "specialty_description             object\n", "description_flag                  object\n", "drug_name                         object\n", "generic_name                      object\n", "bene_count                       float64\n", "total_claim_count                  int64\n", "total_day_supply                   int64\n", "total_drug_cost                  float64\n", "bene_count_ge65                  float64\n", "bene_count_ge65_suppress_flag     object\n", "total_claim_count_ge65           float64\n", "ge65_suppress_flag                object\n", "total_day_supply_ge65            float64\n", "total_drug_cost_ge65             float64\n", "total_30_day_fill_count          float64\n", "total_30_day_fill_count_ge65     float64\n", "dtype: object\n", "\n", "Columns:\n", " ['npi', 'nppes_provider_last_org_name', 'nppes_provider_first_name', 'nppes_provider_city', 'nppes_provider_state', 'specialty_description', 'description_flag', 'drug_name', 'generic_name', 'bene_count', 'total_claim_count', 'total_day_supply', 'total_drug_cost', 'bene_count_ge65', 'bene_count_ge65_suppress_flag', 'total_claim_count_ge65', 'ge65_suppress_flag', 'total_day_supply_ge65', 'total_drug_cost_ge65', 'total_30_day_fill_count', 'total_30_day_fill_count_ge65']\n"]}], "source": ["# Shape\n", "print(\"Shape:\", df.shape)\n", "\n", "# Columns and Data Types\n", "print(\"\\nData Types:\\n\", df.dtypes)\n", "\n", "# Column names\n", "print(\"\\nColumns:\\n\", df.columns.tolist())\n"]}, {"cell_type": "code", "execution_count": 6, "id": "6d22c674-932f-4c35-bf1d-48a0241e6fd2", "metadata": {}, "outputs": [{"data": {"text/plain": ["bene_count_ge65                  20807112\n", "bene_count                       15023286\n", "ge65_suppress_flag               14006302\n", "total_30_day_fill_count_ge65     10114316\n", "total_drug_cost_ge65             10114316\n", "total_day_supply_ge65            10114316\n", "total_claim_count_ge65           10114316\n", "bene_count_ge65_suppress_flag     3313506\n", "nppes_provider_last_org_name          439\n", "nppes_provider_first_name             132\n", "total_drug_cost                         0\n", "total_30_day_fill_count                 0\n", "npi                                     0\n", "total_day_supply                        0\n", "generic_name                            0\n", "drug_name                               0\n", "description_flag                        0\n", "specialty_description                   0\n", "nppes_provider_state                    0\n", "nppes_provider_city                     0\n", "total_claim_count                       0\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isnull().sum().sort_values(ascending=False)\n"]}, {"attachments": {}, "cell_type": "markdown", "id": "750a256c-4a4d-4faf-9eef-77b558292590", "metadata": {}, "source": ["## Missing Data Interpretation\n", "\n", "Columns with large numbers of missing values (e.g., `bene_count_ge65`, `total_claim_count_ge65`, etc.) are only populated for providers who served Medicare patients aged 65+. For other providers, these columns are correctly blank and should be handled accordingly in later analysis. This is not a data quality issue, but rather reflects the structure and applicability of the data.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "cd6034f6-b89a-499c-abb8-72688ccd9b7a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of duplicate rows: 0\n"]}], "source": ["num_duplicates = df.duplicated().sum()\n", "print(f\"Number of duplicate rows: {num_duplicates}\")\n", "\n", "# If there are duplicates, we remove them\n", "if num_duplicates > 0:\n", "    df = df.drop_duplicates()\n", "    print(\"Duplicates removed.\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "2563be43-2515-4a36-b962-c73f827d2233", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top specialties:\n", "specialty_description\n", "Family Practice        7147536\n", "Internal Medicine      6863139\n", "Nurse Practitioner     2072646\n", "Physician Assistant    1120472\n", "Cardiology              985662\n", "Psychiatry              677141\n", "General Practice        529492\n", "Neurology               391213\n", "Emergency Medicine      329242\n", "Nephrology              310888\n", "Name: count, dtype: int64\n", "\n", "Top drugs:\n", "drug_name\n", "HYDROCODONE-ACETAMINOPHEN    332670\n", "LISINOPRIL                   273518\n", "OMEPRAZOLE                   264842\n", "AMLODIPINE BESYLATE          259828\n", "GABAPENTIN                   254001\n", "ATORVASTATIN CALCIUM         247176\n", "FUROSEMIDE                   240799\n", "LEVOTHYROXINE SODIUM         239458\n", "SIMVASTATIN                  239013\n", "METOPROLOL TARTRATE          225246\n", "Name: count, dtype: int64\n", "\n", "Top states:\n", "nppes_provider_state\n", "CA    2314491\n", "NY    1650337\n", "FL    1611230\n", "TX    1472194\n", "PA    1229100\n", "OH    1001853\n", "NC     876296\n", "IL     866555\n", "MI     824210\n", "GA     665123\n", "Name: count, dtype: int64\n"]}], "source": ["print(\"Top specialties:\")\n", "print(df['specialty_description'].value_counts().head(10))\n", "\n", "print(\"\\nTop drugs:\")\n", "print(df['drug_name'].value_counts().head(10))\n", "\n", "print(\"\\nTop states:\")\n", "print(df['nppes_provider_state'].value_counts().head(10))\n"]}, {"cell_type": "markdown", "id": "3ba5a84a-5f0d-4993-97f4-2128613fbb1f", "metadata": {}, "source": ["## Key Categorical Insights\n", "\n", "**Top 10 Specialties:**\n", "1. Family Practice — 7,147,536\n", "2. Internal Medicine — 6,863,139\n", "3. <PERSON> — 2,072,646\n", "4. Physician Assistant — 1,120,472\n", "5. <PERSON><PERSON> — 985,662\n", "6. <PERSON><PERSON><PERSON><PERSON> — 677,141\n", "7. General Practice — 529,492\n", "8. Neur<PERSON> — 391,213\n", "9. Emergency Medicine — 329,242\n", "10. Nephrology — 310,888\n", "\n", "**Top 10 Drugs:**\n", "1. HYDROCODONE-AC<PERSON><PERSON><PERSON>OPHEN — 332,670\n", "2. LISINOPRIL — 273,518\n", "3. OMEPRAZOLE — 264,842\n", "4. AMLODIPINE BESYLATE — 259,828\n", "5. GABAPENTIN — 254,001\n", "6. ATORVASTAT<PERSON> CALCIUM — 247,176\n", "7. FUROSEMIDE — 240,799\n", "8. LEVOTH<PERSON>ROXIN<PERSON> SODIUM — 239,458\n", "9. SIMVASTATIN — 239,013\n", "10. METOPROLOL TARTRATE — 225,246\n", "\n", "**Top 10 States by Provider Count:**\n", "1. CA — 2,314,491\n", "2. NY — 1,650,337\n", "3. FL — 1,611,230\n", "4. TX — 1,472,194\n", "5. PA — 1,229,100\n", "6. OH — 1,001,853\n", "7. <PERSON> <PERSON> 876,296\n", "8. IL — 866,555\n", "9. MI <PERSON> 824,210\n", "10. GA — 665,123\n", "\n", "> **Interpretation:**  \n", "> The dataset is heavily weighted toward providers in Family Practice and Internal Medicine, with nurse practitioners and physician assistants also well represented. The most frequently prescribed drug is HYDROCODONE-ACETAMINOPHEN, followed by several widely used medications for blood pressure, cholesterol, and mental health. California, New York, and Florida have the highest number of providers in this dataset, indicating strong regional representation in these states.\n"]}, {"cell_type": "code", "execution_count": 15, "id": "2f39c4b8-7b48-4903-8985-40e4b463527d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bene_count</th>\n", "      <th>total_claim_count</th>\n", "      <th>total_day_supply</th>\n", "      <th>total_drug_cost</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>9097332.00</td>\n", "      <td>24120618.00</td>\n", "      <td>24120618.00</td>\n", "      <td>24120618.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>28.29</td>\n", "      <td>50.96</td>\n", "      <td>2034.31</td>\n", "      <td>3905.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>34.90</td>\n", "      <td>86.68</td>\n", "      <td>3688.86</td>\n", "      <td>25085.42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>11.00</td>\n", "      <td>11.00</td>\n", "      <td>5.00</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>14.00</td>\n", "      <td>15.00</td>\n", "      <td>450.00</td>\n", "      <td>271.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>19.00</td>\n", "      <td>24.00</td>\n", "      <td>900.00</td>\n", "      <td>725.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>32.00</td>\n", "      <td>50.00</td>\n", "      <td>1980.00</td>\n", "      <td>2517.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>27840.00</td>\n", "      <td>27904.00</td>\n", "      <td>487092.00</td>\n", "      <td>12993071.05</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       bene_count  total_claim_count  total_day_supply  total_drug_cost\n", "count  9097332.00        24120618.00       24120618.00      24120618.00\n", "mean        28.29              50.96           2034.31          3905.82\n", "std         34.90              86.68           3688.86         25085.42\n", "min         11.00              11.00              5.00             0.00\n", "25%         14.00              15.00            450.00           271.55\n", "50%         19.00              24.00            900.00           725.62\n", "75%         32.00              50.00           1980.00          2517.26\n", "max      27840.00           27904.00         487092.00      12993071.05"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df[['bene_count', 'total_claim_count', 'total_day_supply', 'total_drug_cost']].describe()\n"]}, {"cell_type": "markdown", "id": "d3be7622-8d4c-4f96-8679-30d6400af514", "metadata": {}, "source": ["## Checking for negative values for columns that shouldn't have\n", "\n", "The main numeric columns (`bene_count`, `total_claim_count`, `total_day_supply`, `total_drug_cost`) were summarized to check for data quality issues:\n", "\n", "- **No negative values** were found in any of the columns, indicating correct data entry for counts and costs.\n", "- **Extreme high outliers** were observed (e.g., maximum `total_drug_cost` > $13 million, maximum `total_day_supply` > 487,000), which are expected given the scale and nature of Medicare data. These likely reflect providers or claims with exceptionally high volumes or costs, but should be flagged for further review in downstream analyses.\n", "- **Zeros** in `total_drug_cost` are present, which may represent cases with no cost recorded (e.g., free drugs, sample programs, or suppression for privacy). This is typical in healthcare data but should be noted.\n", "\n", "> **Conclusion:** The distributions of the main numeric columns appear reasonable for national-scale outpatient claims data. Outliers and zero-cost claims are expected and will be considered in subsequent modeling and analysis steps.\n"]}, {"cell_type": "code", "execution_count": 13, "id": "849f727c-a39d-4012-81fb-69e7f453a4d6", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(8, 5))\n", "# Add 1 to avoid log(0); or filter zeros if appropriate\n", "np.log10(df['total_drug_cost'].sample(100_000, random_state=42) + 1).hist(bins=100)\n", "plt.title(\"Distribution of Log-Transformed Total Drug Cost\")\n", "plt.xlabel(\"log10(Total Drug Cost + 1)\")\n", "plt.ylabel(\"Frequency\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 14, "id": "1ce3b863-09eb-41c0-b3fe-2e1e21194836", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["subset = df[df['total_drug_cost'] < 10000]['total_drug_cost'].sample(100_000, random_state=42)\n", "subset.hist(bins=100)\n", "plt.title(\"Distribution of Total Drug Cost (< $10,000)\")\n", "plt.xlabel(\"Total Drug Cost\")\n", "plt.ylabel(\"Frequency\")\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "f8d67809-3079-492d-a081-77c1ede1bd9c", "metadata": {}, "source": ["## Distribution of Total Drug Cost\n", "\n", "The distribution of `total_drug_cost` is extremely right-skewed, as expected in large-scale medical claims data. Most claims are concentrated at lower cost values, with a long tail of high-cost outliers.\n", "\n", "To make the distribution of `total_drug_cost` easier to understand:\n", "\n", "- **Log transformation:** Plotting the log of total drug cost (`log10(total_drug_cost + 1)`) spreads out the values and reveals that most claims are grouped in a central range. This makes the pattern more visible compared to the raw data.\n", "- **Filtering claims below \\$10,000:** Focusing only on claims under 10,000 dollars shows that the vast majority of claims are small (mostly under $2,000), with fewer high-cost claims.\n", "\n", "These techniques help reveal the true shape of the data, which is hard to see when plotting the raw numbers due to extreme outliers.\n", "\n", "> These findings confirm that the dataset follows typical healthcare cost patterns, with a majority of claims at lower costs and a minority of high-value outliers. For modeling, log-transformed costs may provide more stable and interpretable results.\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}